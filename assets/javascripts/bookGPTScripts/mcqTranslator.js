document.addEventListener('DOMContentLoaded', function() {
    var form = document.getElementById('translatorForm');
    var loaderContainer = document.getElementById('mt_loaderContainer');
    var loaderTitle = document.getElementById('loaderTitle');
    var loaderSubtitle = document.getElementById('loaderSubtitle');
    var progressBarFill = document.getElementById('progressBarFill');
    var progressText = document.getElementById('progressText');
    var logContainer = document.getElementById('logContainer');
    var progressLog = document.getElementById('progressLog');
    var resultContainer = document.getElementById('resultContainer');
    var translationResults = document.getElementById('translationResults');

    function addLogEntry(message) {
        var entry = document.createElement('div');
        entry.className = 'log-entry';
        entry.textContent = message;
        progressLog.appendChild(entry);
        progressLog.scrollTop = progressLog.scrollHeight;
    }

    function updateLoader(title, subtitle, progress) {
        loaderTitle.textContent = title;
        loaderSubtitle.textContent = subtitle;
        progressBarFill.style.width = progress + '%';
        progressText.textContent = progress + '%';
    }

    function showLoader() {
        loaderContainer.style.display = 'block';
        updateLoader('Initializing Translation...', 'Preparing to process your PDF file', 0);
    }

    function hideLoader() {
        loaderContainer.style.display = 'none';
    }

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Clear previous results
        progressLog.innerHTML = '';
        translationResults.innerHTML = '';
        resultContainer.style.display = 'none';

        // Show loader and log container
        showLoader();
        logContainer.style.display = 'block';

        // Get form values
        var pdfFile = document.getElementById('pdfFile').files[0];
        var totalQuestions = document.getElementById('totalQuestions').value;
        var sourceLanguage = document.getElementById('sourceLanguage').value;
        var destinationLanguage = document.getElementById('destinationLanguage').value;

        // Validate inputs
        if (!pdfFile || !totalQuestions || !sourceLanguage || !destinationLanguage) {
            addLogEntry('Error: All fields are required');
            hideLoader();
            return;
        }

        if (sourceLanguage === destinationLanguage) {
            addLogEntry('Error: Source and destination languages must be different');
            hideLoader();
            return;
        }

        addLogEntry('Starting translation for file: ' + pdfFile.name);
        addLogEntry('Source language: ' + sourceLanguage + ', Destination language: ' + destinationLanguage);

        // Start progress simulation
        var currentProgress = 0;
        var progressInterval = null;
        var progressSteps = [
            { progress: 10, title: 'Uploading PDF File...', subtitle: 'Sending your file to the server' },
            { progress: 25, title: 'Converting PDF to Images...', subtitle: 'Processing PDF pages into column images' },
            { progress: 45, title: 'Extracting Text Content...', subtitle: 'Using AI to extract text from images' },
            { progress: 65, title: 'Translating Content...', subtitle: 'Translating from ' + sourceLanguage + ' to ' + destinationLanguage },
            { progress: 85, title: 'Finalizing Translation...', subtitle: 'Combining and uploading translated content' },
            { progress: 95, title: 'Almost Complete...', subtitle: 'Preparing final results' }
        ];

        var stepIndex = 0;
        progressInterval = setInterval(function() {
            if (stepIndex < progressSteps.length) {
                var step = progressSteps[stepIndex];
                updateLoader(step.title, step.subtitle, step.progress);
                currentProgress = step.progress;
                stepIndex++;
            }
        }, 5000); // Update every 5 seconds

        // Create FormData for file upload
        var formData = new FormData();
        formData.append('pdfFile', pdfFile);
        formData.append('total_questions', totalQuestions);
        formData.append('source_language', sourceLanguage);
        formData.append('destination_language', destinationLanguage);
        formData.append('username', 'web_user');

        // Call the API to start translation
        fetch('/pdfExtractor/mcqTranslatorFile', {
            method: 'POST',
            body: formData
        })
            .then(function(response) {
                return response.json();
            })
            .then(function(data) {
                // Clear the progress interval
                if (progressInterval) {
                    clearInterval(progressInterval);
                    progressInterval = null;
                }

                if (data.status === 'success') {
                    // Complete the progress bar
                    updateLoader('Translation Complete!', 'Your MCQ translation has been completed successfully', 100);

                    // Hide loader after a short delay to show completion
                    setTimeout(function() {
                        hideLoader();
                    }, 1500);

                    addLogEntry('Translation completed successfully!');
                    addLogEntry('Translation ID: ' + data.translation_id);
                    addLogEntry('Original S3 Path: ' + data.original_s3_path);
                    if (data.translated_s3_path) {
                        addLogEntry('Translated S3 Path: ' + data.translated_s3_path);
                    }

                    // Show the translation results
                    resultContainer.style.display = 'block';

                    // Create buttons container
                    var buttonsContainer = document.createElement('div');
                    buttonsContainer.className = 'buttons-container';

                    // Create a button to view the translated content
                    var viewButton = document.createElement('button');
                    viewButton.textContent = 'View Translated Content';
                    viewButton.className = 'view-button';
                    viewButton.addEventListener('click', function() {
                        // Show loading state on button
                        var originalText = viewButton.textContent;
                        viewButton.textContent = 'Loading...';
                        viewButton.disabled = true;

                        addLogEntry('Fetching translated content...');

                        fetch('/pdfExtractor/getTranslatedMcqContent/' + data.translation_id)
                            .then(function(response) {
                                return response.json();
                            })
                            .then(function(contentData) {
                                if (contentData.status === 'success') {
                                    // Clear previous content
                                    translationResults.innerHTML = '';
                                    translationResults.appendChild(buttonsContainer);

                                    // Add the translated text in a pre element
                                    var textPre = document.createElement('pre');
                                    textPre.className = 'translation-text';
                                    textPre.textContent = contentData.content;
                                    translationResults.appendChild(textPre);

                                    addLogEntry('Translated content loaded successfully');
                                } else {
                                    addLogEntry('Error fetching translated content: ' + contentData.message);
                                }
                            })
                            .catch(function(error) {
                                addLogEntry('Error: ' + error.message);
                            })
                            .finally(function() {
                                // Restore button state
                                viewButton.textContent = originalText;
                                viewButton.disabled = false;
                            });
                    });

                    // Create download button
                    var downloadButton = document.createElement('button');
                    downloadButton.textContent = 'Download Translated File';
                    downloadButton.className = 'download-button';
                    downloadButton.addEventListener('click', function() {
                        // Show loading state on button
                        var originalText = downloadButton.textContent;
                        downloadButton.textContent = 'Downloading...';
                        downloadButton.disabled = true;

                        var downloadUrl = '/pdfExtractor/downloadTranslatedMcq/' + data.translation_id;
                        window.open(downloadUrl, '_blank');
                        addLogEntry('Download started...');

                        // Restore button state after a short delay
                        setTimeout(function() {
                            downloadButton.textContent = originalText;
                            downloadButton.disabled = false;
                        }, 2000);
                    });

                    // Create link display and copy functionality
                    var linkContainer = document.createElement('div');
                    linkContainer.className = 'link-container';

                    var linkLabel = document.createElement('label');
                    linkLabel.textContent = 'Download Link:';

                    var linkInput = document.createElement('input');
                    linkInput.type = 'text';
                    linkInput.className = 'link-input';
                    linkInput.value = window.location.origin + '/pdfExtractor/downloadTranslatedMcq/' + data.translation_id;
                    linkInput.readOnly = true;

                    var copyButton = document.createElement('button');
                    copyButton.textContent = 'Copy Link';
                    copyButton.className = 'copy-button';
                    copyButton.addEventListener('click', function() {
                        linkInput.select();
                        document.execCommand('copy');
                        copyButton.textContent = 'Copied!';
                        setTimeout(function() {
                            copyButton.textContent = 'Copy Link';
                        }, 2000);
                    });

                    // Add elements to containers
                    buttonsContainer.appendChild(viewButton);
                    buttonsContainer.appendChild(downloadButton);

                    linkContainer.appendChild(linkLabel);
                    linkContainer.appendChild(linkInput);
                    linkContainer.appendChild(copyButton);

                    translationResults.appendChild(buttonsContainer);
                    translationResults.appendChild(linkContainer);

                } else {
                    // Clear the progress interval and hide loader on error
                    if (progressInterval) {
                        clearInterval(progressInterval);
                        progressInterval = null;
                    }
                    updateLoader('Translation Failed', 'An error occurred during translation', currentProgress);
                    setTimeout(function() {
                        hideLoader();
                    }, 2000);
                    addLogEntry('Error: ' + data.message);
                }
            })
            .catch(function(error) {
                // Clear the progress interval and hide loader on error
                if (progressInterval) {
                    clearInterval(progressInterval);
                    progressInterval = null;
                }
                updateLoader('Connection Error', 'Failed to connect to the server', currentProgress);
                setTimeout(function() {
                    hideLoader();
                }, 2000);
                addLogEntry('Error: ' + error.message);
            });
    });
})