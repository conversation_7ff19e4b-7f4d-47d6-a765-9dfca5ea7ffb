<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js" defer></script>

<style>
/* Loader Styles */
.mt_loader-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.mt_loader {
    width: 60px;
    height: 60px;
    border: 6px solid #f3f3f3;
    border-top: 6px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.mt_loader-text {
    flex: 1;
    max-width: 400px;
}

.mt_loader-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.mt_loader-subtitle {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.mt_progress-bar-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.mt_progress-bar {
    flex: 1;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.mt_progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.mt_progress-text {
    font-size: 12px;
    font-weight: bold;
    color: #4CAF50;
    min-width: 35px;
}

.translation-results {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.metadata {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.translation-text {
    white-space: pre-wrap;
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 5px;
    max-height: 500px;
    overflow-y: auto;
}

.buttons-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.view-button, .download-button {
    background-color: #2196F3;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.view-button:hover, .download-button:hover {
    background-color: #45a049;
}

.view-button:disabled, .download-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    opacity: 0.6;
}

.view-button:disabled:hover, .download-button:disabled:hover {
    background-color: #cccccc;
}

.download-button {
    background-color: #2196F3;
}

.download-button:hover {
    background-color: #1976D2;
}

.link-container {
    margin-top: 15px;
    padding: 15px;
    background-color: #f0f0f0;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.link-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.link-input {
    width: calc(100% - 100px);
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-right: 10px;
    font-family: monospace;
    font-size: 14px;
}

.copy-button {
    background-color: #FF9800;
    color: white;
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.copy-button:hover {
    background-color: #F57C00;
}

select[name='sourceLanguage'],
select[name='destinationLanguage']{
    padding: 0.6rem !important;
    background: #f4f4f4;
    border-radius: 6px !important;
    border: 1px solid #ccc !important;
    font-size: 1rem !important;
    margin-bottom: 1.2rem !important;
}

.test-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: Arial, sans-serif;
}

.test-card {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.test-description {
    color: #666;
    margin-bottom: 30px;
    font-size: 16px;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.login-form label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.login-form input[type="file"],
.login-form input[type="number"],
.login-form select {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    background: #f9f9f9;
}

.login-form button[type="submit"] {
    background-color: #2196F3;
    color: white;
    padding: 10px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
}

.login-form button[type="submit"]:hover {
    background-color: #45a049;
}

.progress-log {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 15px;
    max-height: 200px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 14px;
}

.log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
    border-bottom: 1px solid #eee;
}
.pyqs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}
</style>
<%
    String backNav
    if ('books'.equals(session['entryController'])){
        backNav = "/${session['entryController']}/home"
    }else if('privatelabel'.equals(session['entryController'])){
        backNav = "/${session['entryController']}/admin"
    }
%>
<div class="test-container">
    <div class="pyqs-header mt-5">
        <a href="${backNav}" class="back-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
            </svg>
            Back to Admin
        </a>
    </div>
    <div class="test-card">
        <h1>MCQ Translator</h1>
        <p class="test-description">Translate MCQs from one language to another</p>

        <form id="translatorForm" class="login-form" enctype="multipart/form-data">
            <label for="pdfFile">Upload PDF File</label>
            <input type="file" id="pdfFile" name="pdfFile" accept=".pdf" required>

            <label for="totalQuestions">Total Questions</label>
            <input type="number" id="totalQuestions" name="totalQuestions" min="1" value="10" required>

            <label for="sourceLanguage">Source Language</label>
            <select id="sourceLanguage" name="sourceLanguage" required>
                <option value="English">English</option>
                <option value="Hindi">Hindi</option>
                <option value="Tamil">Tamil</option>
                <option value="Assamese">Assamese</option>
                <option value="Telugu">Telugu</option>
                <option value="Kannada">Kannada</option>
                <option value="Malayalam">Malayalam</option>
                <option value="Marathi">Marathi</option>
                <option value="Bengali">Bengali</option>
                <option value="Gujarati">Gujarati</option>
                <option value="Urdu">Urdu</option>
            </select>

            <label for="destinationLanguage">Destination Language</label>
            <select id="destinationLanguage" name="destinationLanguage" required>
                <option value="Hindi">Hindi</option>
                <option value="English">English</option>
                <option value="Tamil">Tamil</option>
                <option value="Assamese">Assamese</option>
                <option value="Telugu">Telugu</option>
                <option value="Kannada">Kannada</option>
                <option value="Malayalam">Malayalam</option>
                <option value="Marathi">Marathi</option>
                <option value="Bengali">Bengali</option>
                <option value="Gujarati">Gujarati</option>
                <option value="Urdu">Urdu</option>
            </select>

            <button type="submit">Translate MCQ</button>
        </form>
        <div id="loaderContainer" style="display: none; margin-top: 20px;">
            <div class="loader-wrapper">
                <div class="loader"></div>
                <div class="loader-text">
                    <div id="loaderTitle">Processing Translation...</div>
                    <div id="loaderSubtitle">Please wait while we process your PDF file</div>
                    <div class="progress-bar-container" style="display: none;">
                        <div class="progress-bar">
                            <div id="progressBarFill" class="progress-bar-fill"></div>
                        </div>
                        <div id="progressText" class="progress-text">0%</div>
                    </div>
                </div>
            </div>
        </div>

        <div id="logContainer" style="display: none; margin-top: 20px;">
            <h3>Progress Log: <span id="timer" style="color: #007bff; font-weight: bold;">00:00</span></h3>
            <div id="progressLog" class="progress-log"></div>
        </div>

        <div id="resultContainer" style="display: none; margin-top: 20px;">
            <h3>Translation Results:</h3>
            <div id="translationResults" class="translation-results"></div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var form = document.getElementById('translatorForm');
        var loaderContainer = document.getElementById('mt_loaderContainer');
        var loaderTitle = document.getElementById('loaderTitle');
        var loaderSubtitle = document.getElementById('loaderSubtitle');
        var progressBarFill = document.getElementById('progressBarFill');
        var progressText = document.getElementById('progressText');
        var logContainer = document.getElementById('logContainer');
        var progressLog = document.getElementById('progressLog');
        var resultContainer = document.getElementById('resultContainer');
        var translationResults = document.getElementById('translationResults');

        function addLogEntry(message) {
            var entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = message;
            progressLog.appendChild(entry);
            progressLog.scrollTop = progressLog.scrollHeight;
        }

        function updateLoader(title, subtitle, progress) {
            loaderTitle.textContent = title;
            loaderSubtitle.textContent = subtitle;
            progressBarFill.style.width = progress + '%';
            progressText.textContent = progress + '%';
        }

        function showLoader() {
            loaderContainer.style.display = 'block';
            updateLoader('Initializing Translation...', 'Preparing to process your PDF file', 0);
        }

        function hideLoader() {
            loaderContainer.style.display = 'none';
        }

        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Clear previous results
            progressLog.innerHTML = '';
            translationResults.innerHTML = '';
            resultContainer.style.display = 'none';

            // Show loader and log container
            showLoader();
            logContainer.style.display = 'block';

            // Get form values
            var pdfFile = document.getElementById('pdfFile').files[0];
            var totalQuestions = document.getElementById('totalQuestions').value;
            var sourceLanguage = document.getElementById('sourceLanguage').value;
            var destinationLanguage = document.getElementById('destinationLanguage').value;

            // Validate inputs
            if (!pdfFile || !totalQuestions || !sourceLanguage || !destinationLanguage) {
                addLogEntry('Error: All fields are required');
                hideLoader();
                return;
            }

            if (sourceLanguage === destinationLanguage) {
                addLogEntry('Error: Source and destination languages must be different');
                hideLoader();
                return;
            }

            addLogEntry('Starting translation for file: ' + pdfFile.name);
            addLogEntry('Source language: ' + sourceLanguage + ', Destination language: ' + destinationLanguage);

            // Start progress simulation
            var currentProgress = 0;
            var progressInterval = null;
            var progressSteps = [
                { progress: 10, title: 'Uploading PDF File...', subtitle: 'Sending your file to the server' },
                { progress: 25, title: 'Converting PDF to Images...', subtitle: 'Processing PDF pages into column images' },
                { progress: 45, title: 'Extracting Text Content...', subtitle: 'Using AI to extract text from images' },
                { progress: 65, title: 'Translating Content...', subtitle: 'Translating from ' + sourceLanguage + ' to ' + destinationLanguage },
                { progress: 85, title: 'Finalizing Translation...', subtitle: 'Combining and uploading translated content' },
                { progress: 95, title: 'Almost Complete...', subtitle: 'Preparing final results' }
            ];

            var stepIndex = 0;
            progressInterval = setInterval(function() {
                if (stepIndex < progressSteps.length) {
                    var step = progressSteps[stepIndex];
                    updateLoader(step.title, step.subtitle, step.progress);
                    currentProgress = step.progress;
                    stepIndex++;
                }
            }, 5000); // Update every 5 seconds

            // Create FormData for file upload
            var formData = new FormData();
            formData.append('pdfFile', pdfFile);
            formData.append('total_questions', totalQuestions);
            formData.append('source_language', sourceLanguage);
            formData.append('destination_language', destinationLanguage);
            formData.append('username', 'web_user');

            // Call the API to start translation
            fetch('/pdfExtractor/mcqTranslatorFile', {
                method: 'POST',
                body: formData
            })
            .then(function(response) {
                return response.json();
            })
            .then(function(data) {
                // Clear the progress interval
                if (progressInterval) {
                    clearInterval(progressInterval);
                    progressInterval = null;
                }

                if (data.status === 'success') {
                    // Complete the progress bar
                    updateLoader('Translation Complete!', 'Your MCQ translation has been completed successfully', 100);

                    // Hide loader after a short delay to show completion
                    setTimeout(function() {
                        hideLoader();
                    }, 1500);

                    addLogEntry('Translation completed successfully!');
                    addLogEntry('Translation ID: ' + data.translation_id);
                    addLogEntry('Original S3 Path: ' + data.original_s3_path);
                    if (data.translated_s3_path) {
                        addLogEntry('Translated S3 Path: ' + data.translated_s3_path);
                    }

                    // Show the translation results
                    resultContainer.style.display = 'block';

                    // Create buttons container
                    var buttonsContainer = document.createElement('div');
                    buttonsContainer.className = 'buttons-container';

                    // Create a button to view the translated content
                    var viewButton = document.createElement('button');
                    viewButton.textContent = 'View Translated Content';
                    viewButton.className = 'view-button';
                    viewButton.addEventListener('click', function() {
                        // Show loading state on button
                        var originalText = viewButton.textContent;
                        viewButton.textContent = 'Loading...';
                        viewButton.disabled = true;

                        addLogEntry('Fetching translated content...');

                        fetch('/pdfExtractor/getTranslatedMcqContent/' + data.translation_id)
                        .then(function(response) {
                            return response.json();
                        })
                        .then(function(contentData) {
                            if (contentData.status === 'success') {
                                // Clear previous content
                                translationResults.innerHTML = '';
                                translationResults.appendChild(buttonsContainer);

                                // Add the translated text in a pre element
                                var textPre = document.createElement('pre');
                                textPre.className = 'translation-text';
                                textPre.textContent = contentData.content;
                                translationResults.appendChild(textPre);

                                addLogEntry('Translated content loaded successfully');
                            } else {
                                addLogEntry('Error fetching translated content: ' + contentData.message);
                            }
                        })
                        .catch(function(error) {
                            addLogEntry('Error: ' + error.message);
                        })
                        .finally(function() {
                            // Restore button state
                            viewButton.textContent = originalText;
                            viewButton.disabled = false;
                        });
                    });

                    // Create download button
                    var downloadButton = document.createElement('button');
                    downloadButton.textContent = 'Download Translated File';
                    downloadButton.className = 'download-button';
                    downloadButton.addEventListener('click', function() {
                        // Show loading state on button
                        var originalText = downloadButton.textContent;
                        downloadButton.textContent = 'Downloading...';
                        downloadButton.disabled = true;

                        var downloadUrl = '/pdfExtractor/downloadTranslatedMcq/' + data.translation_id;
                        window.open(downloadUrl, '_blank');
                        addLogEntry('Download started...');

                        // Restore button state after a short delay
                        setTimeout(function() {
                            downloadButton.textContent = originalText;
                            downloadButton.disabled = false;
                        }, 2000);
                    });

                    // Create link display and copy functionality
                    var linkContainer = document.createElement('div');
                    linkContainer.className = 'link-container';

                    var linkLabel = document.createElement('label');
                    linkLabel.textContent = 'Download Link:';

                    var linkInput = document.createElement('input');
                    linkInput.type = 'text';
                    linkInput.className = 'link-input';
                    linkInput.value = window.location.origin + '/pdfExtractor/downloadTranslatedMcq/' + data.translation_id;
                    linkInput.readOnly = true;

                    var copyButton = document.createElement('button');
                    copyButton.textContent = 'Copy Link';
                    copyButton.className = 'copy-button';
                    copyButton.addEventListener('click', function() {
                        linkInput.select();
                        document.execCommand('copy');
                        copyButton.textContent = 'Copied!';
                        setTimeout(function() {
                            copyButton.textContent = 'Copy Link';
                        }, 2000);
                    });

                    // Add elements to containers
                    buttonsContainer.appendChild(viewButton);
                    buttonsContainer.appendChild(downloadButton);

                    linkContainer.appendChild(linkLabel);
                    linkContainer.appendChild(linkInput);
                    linkContainer.appendChild(copyButton);

                    translationResults.appendChild(buttonsContainer);
                    translationResults.appendChild(linkContainer);

                } else {
                    // Clear the progress interval and hide loader on error
                    if (progressInterval) {
                        clearInterval(progressInterval);
                        progressInterval = null;
                    }
                    updateLoader('Translation Failed', 'An error occurred during translation', currentProgress);
                    setTimeout(function() {
                        hideLoader();
                    }, 2000);
                    addLogEntry('Error: ' + data.message);
                }
            })
            .catch(function(error) {
                // Clear the progress interval and hide loader on error
                if (progressInterval) {
                    clearInterval(progressInterval);
                    progressInterval = null;
                }
                updateLoader('Connection Error', 'Failed to connect to the server', currentProgress);
                setTimeout(function() {
                    hideLoader();
                }, 2000);
                addLogEntry('Error: ' + error.message);
            });
        });
    })
</script>
